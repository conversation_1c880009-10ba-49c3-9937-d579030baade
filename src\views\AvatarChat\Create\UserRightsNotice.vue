<script setup lang="ts">
  import { ref } from 'vue';
  import { ExclamationCircleOutlined } from '@ant-design/icons-vue';

  const props = defineProps<{
    visible?: boolean;
    handleCloseModal?: () => void;
  }>();

  const emit = defineEmits(['close', 'agree']);

  const isOpenModal = ref(false);

  const openModal = () => {
    isOpenModal.value = true;
  };

  defineExpose({
    openModal,
  });

  const handleOk = () => {
    isOpenModal.value = false;
    emit('agree');
    emit('close');
  };

  const handleCancel = () => {
    isOpenModal.value = false;
    emit('close');
  };
</script>

<template>
  <a-modal
    :open="isOpenModal"
    :footer="null"
    :width="450"
    :mask-closable="false"
    centered
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <template #title>
      <ExclamationCircleOutlined style="margin-right: 8px" />
      使用者承诺须知
    </template>
    <div class="user-rights-notice">
      <div class="notice-content">
        <p class="notice-text">
          本平台所有功能均受到知识产权法等相关法律平台（下称"本平台"）使用相关工具且代理
          管理您的作品，您同意并授权本平台为您在知识产权方面提供以下内容：
        </p>

        <p class="notice-text">
          • 您有为用户在本平台上上传、发布的作品，应当具备、完整的知识产权，不得侵犯
          他人知识产权或其他任何权利；
        </p>

        <p class="notice-text">
          • 您在使用本平台上上传、发布时，应当保证国家法律、法规、遵守公共秩
          序、善良风俗，不得利用、传播利益、暴力、恐怖、迷信等违法和不良信息，不得实施违法犯罪活
          动等违法要求，如有违反，一经本平台发现或接到举报后经审核属实的，本平台有权立即删除相关
          下架、禁止上传内容，封禁违规账号等处理方式，如造成损失的，您应当承担全部责任；
        </p>

        <p class="notice-text">
          • 若您上传作品涉及他人肖像的（包括但不限于手绘、文字、声音、音频、图片、
          动画等）您应当获得第三方权利，本平台为您代为申请相关权限及权限对象等对象等
          的合法授权，若您违反上述规定导致纠纷的，您应当承担全部责任，与本平台无关，且应当赔偿本平台因
          此遭受的损失；
        </p>

        <p class="notice-text">
          • 请勿擅自他人肖像权等相关权利，请您遵守相关法律法规，请您遵守伦理，或任何中华人民
          共和国法律法规的内容，我们有权拒绝所有内容并带来相关损失，以及可能造成的任何损失，
          及可能造成的任何损失，请您遵守相关法律法规的内容；
        </p>

        <p class="notice-text">
          • 更多信息请参见<a href="#" class="link-text">用户协议</a>、<a href="#" class="link-text">隐私协议</a>。
        </p>
      </div>

      <div class="notice-footer">
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" @click="handleOk">我已知晓，同意</a-button>
      </div>
    </div>
  </a-modal>
</template>

<style lang="less" scoped>
.user-rights-notice {
  .notice-content {
    max-height: 400px;
    overflow-y: auto;
    padding: 16px 0;

    .notice-text {
      font-size: 14px;
      line-height: 1.6;
      color: #333;
      margin-bottom: 12px;
      text-align: justify;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .link-text {
      color: #1890ff;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .notice-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 24px;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;
  }
}
</style>
